{
  app: {
    signingConfigs: [],
    products: [
      {
        name: "default",
        signingConfig: "default",
        compatibleSdkVersion: "5.0.4(16)",
        runtimeOS: "HarmonyOS",
        buildOption: {
          strictMode: {
            caseSensitiveCheck: true,
            useNormalizedOHMUrl: true,
          },
        },
      },
    ],
    buildModeSet: [
      {
        name: "debug",
      },
      {
        name: "release",
      },
    ],
  },
  modules: [
    {
      name: "entry",
      srcPath: "./products/entry",
      targets: [
        {
          name: "default",
          applyToProducts: ["default"],
        },
      ],
    },
    {
      name: "commonlib",
      srcPath: "./commons/commonlib",
    },
    {
      name: "home",
      srcPath: "./features/home",
    },
    {
      name: "mine",
      srcPath: "./features/mine",
    },
    {
      name: "network",
      srcPath: "./commons/network",
    },
    {
      name: "base_ui",
      srcPath: "./components/base_ui",
    },
    {
      name: "personal_homepage",
      srcPath: "./components/personal_homepage",
    },
  ],
}
