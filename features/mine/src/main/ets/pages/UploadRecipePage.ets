import { UploadRecipeVM } from '../viewModels/UploadRecipeVM';
import { LengthMetrics } from '@kit.ArkUI';
import { buildTitleBar } from 'commonlib';

@Builder
export function UploadRecipeBuilder() {
  UploadRecipePage();
}

@ComponentV2
struct UploadRecipePage {
  vm: UploadRecipeVM = new UploadRecipeVM();

  build() {
    NavDestination() {
      Column() {
        Text('上传功能暂未实现')
          .fontSize(18)
          .fontColor('#666666')
          .margin({ top: 100 })
      }
      .width('100%')
      .height('100%')
      .justifyContent(FlexAlign.Center)
    }
    .title(buildTitleBar('上传菜谱'), { paddingStart: LengthMetrics.vp(16) });
  }

}
